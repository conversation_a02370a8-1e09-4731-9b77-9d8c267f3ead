/* eslint-disable no-undef */
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const PmOrderManageEquipment = require('../models/pm-order-manage-equipment.model');

/**
 * Improved seeder that handles equipment existing in both WM and PM tables
 * Uses timestamp-based priority to determine which reason data to use
 */
const migrateEquipmentReasonsImproved = async () => {
  try {
    console.log('Starting improved equipment reasons migration...');

    // Map to store the most recent reason data for each equipment
    const equipmentReasonMap = new Map();

    // Step 1: Collect WM data with timestamps
    console.log('\n--- Step 1: Collecting WM Flow Data ---');
    await collectWMReasonData(equipmentReasonMap);

    // Step 2: Collect PM data with timestamps (may override WM if more recent)
    console.log('\n--- Step 2: Collecting PM Flow Data ---');
    await collectPMReasonData(equipmentReasonMap);

    // Step 3: Apply the most recent reason data to equipment
    console.log('\n--- Step 3: Applying Updates to Equipment ---');
    await applyEquipmentUpdates(equipmentReasonMap);

    console.log('\n✅ Improved migration completed successfully!');
  } catch (error) {
    console.error('❌ Error during improved migration:', error);
    throw error;
  }
};

/**
 * Collect reason data from WM flow (equipment-order-history)
 */
const collectWMReasonData = async equipmentReasonMap => {
  const equipmentHistories = await EquipmentOrderHistory.find({
    $or: [
      { wmDispatchReasonStatus: { $ne: '' } },
      { wmDispatchReason: { $ne: null } },
      { checkinReasonStatus: { $ne: '' } },
      { checkinReason: { $ne: null } },
    ],
    deletedAt: null,
  })
    .populate('equipment')
    .sort({ updatedAt: -1 }); // Most recent first

  console.log(`Found ${equipmentHistories.length} WM records with reason data`);

  for (const history of equipmentHistories) {
    if (!history.equipment) continue;

    const equipmentId = history.equipment._id.toString();
    const timestamp = history.updatedAt || history.createdAt;

    // Determine reason data (priority: wmDispatch > checkin)
    let reasonData = null;

    if (history.wmDispatchReasonStatus && history.wmDispatchReasonStatus !== '') {
      reasonData = {
        reasonStatus: history.wmDispatchReasonStatus,
        reason: history.wmDispatchReason,
        condition: getConditionFromSubStatus(history.subStatus),
        timestamp: timestamp,
        source: 'WM-Dispatch',
        sourceId: history._id,
      };
    } else if (history.checkinReasonStatus && history.checkinReasonStatus !== '') {
      reasonData = {
        reasonStatus: history.checkinReasonStatus,
        reason: history.checkinReason,
        condition: getConditionFromSubStatus(history.subStatus),
        timestamp: timestamp,
        source: 'WM-Checkin',
        sourceId: history._id,
      };
    }

    if (reasonData) {
      const existing = equipmentReasonMap.get(equipmentId);

      // Use this data if it's the first entry or more recent
      if (!existing || timestamp > existing.timestamp) {
        equipmentReasonMap.set(equipmentId, reasonData);
        console.log(
          `📝 WM: Equipment ${equipmentId} - ${reasonData.source} (${timestamp.toISOString()})`
        );
      } else {
        console.log(
          `⏭️  WM: Equipment ${equipmentId} - Skipped (older than existing ${existing.source})`
        );
      }
    }
  }
};

/**
 * Collect reason data from PM flow (pm-order-manage-equipment)
 */
const collectPMReasonData = async equipmentReasonMap => {
  const pmOrderManageEquipments = await PmOrderManageEquipment.find({
    $or: [
      { checkinReasonStatus: { $ne: '' } },
      { checkinReason: { $ne: null } },
      { checkoutReasonStatus: { $ne: '' } },
      { checkoutReason: { $ne: null } },
    ],
    deletedAt: null,
  })
    .populate('equipment')
    .sort({ updatedAt: -1 }); // Most recent first

  console.log(`Found ${pmOrderManageEquipments.length} PM records with reason data`);

  for (const pmOrderManage of pmOrderManageEquipments) {
    if (!pmOrderManage.equipment || pmOrderManage.equipment.length === 0) continue;

    const timestamp = pmOrderManage.updatedAt || pmOrderManage.createdAt;

    // Determine reason data (priority: checkout > checkin)
    let reasonData = null;

    if (pmOrderManage.checkoutReasonStatus && pmOrderManage.checkoutReasonStatus !== '') {
      reasonData = {
        reasonStatus: pmOrderManage.checkoutReasonStatus,
        reason: pmOrderManage.checkoutReason,
        condition: getConditionFromStatus(pmOrderManage.checkoutStatus),
        timestamp: timestamp,
        source: 'PM-Checkout',
        sourceId: pmOrderManage._id,
      };
    } else if (pmOrderManage.checkinReasonStatus && pmOrderManage.checkinReasonStatus !== '') {
      reasonData = {
        reasonStatus: pmOrderManage.checkinReasonStatus,
        reason: pmOrderManage.checkinReason,
        condition: getConditionFromStatus(pmOrderManage.receivedStatus),
        timestamp: timestamp,
        source: 'PM-Checkin',
        sourceId: pmOrderManage._id,
      };
    }

    if (reasonData) {
      // Apply to all equipment in this PM order
      for (const equipmentId of pmOrderManage.equipment) {
        const equipmentIdStr = equipmentId.toString();
        const existing = equipmentReasonMap.get(equipmentIdStr);

        // Use this data if it's the first entry or more recent
        if (!existing || timestamp > existing.timestamp) {
          equipmentReasonMap.set(equipmentIdStr, reasonData);
          console.log(
            `📝 PM: Equipment ${equipmentIdStr} - ${reasonData.source} (${timestamp.toISOString()})`
          );

          if (existing) {
            console.log(`   ↳ Overrode ${existing.source} (${existing.timestamp.toISOString()})`);
          }
        } else {
          console.log(
            `⏭️  PM: Equipment ${equipmentIdStr} - Skipped (older than existing ${existing.source})`
          );
        }
      }
    }
  }
};

/**
 * Apply the collected reason data to equipment records
 */
const applyEquipmentUpdates = async equipmentReasonMap => {
  let updatedCount = 0;
  let skippedCount = 0;

  console.log(`Applying updates to ${equipmentReasonMap.size} equipment records...`);

  for (const [equipmentId, reasonData] of equipmentReasonMap) {
    try {
      await Equipment.findByIdAndUpdate(equipmentId, {
        $set: {
          reasonStatus: reasonData.reasonStatus,
          reason: reasonData.reason,
          condition: reasonData.condition,
          updatedAt: new Date(),
        },
      });

      console.log(`✅ Updated equipment ${equipmentId}:`);
      console.log(`   Source: ${reasonData.source}`);
      console.log(`   Condition: ${reasonData.condition}`);
      console.log(`   Reason: ${reasonData.reasonStatus} - ${reasonData.reason}`);
      console.log(`   Timestamp: ${reasonData.timestamp.toISOString()}`);

      updatedCount++;
    } catch (error) {
      console.error(`❌ Error updating equipment ${equipmentId}:`, error.message);
      skippedCount++;
    }
  }

  console.log('\n📊 Migration Summary:');
  console.log(`✅ Updated: ${updatedCount} equipment records`);
  console.log(`❌ Skipped: ${skippedCount} records`);
};

/**
 * Helper function to get condition from subStatus
 */
const getConditionFromSubStatus = subStatus => {
  if (subStatus === 'quarantine') return 'quarantine';
  if (subStatus === 'write-off') return 'write-off';
  return 'ok';
};

/**
 * Helper function to get condition from status fields
 */
const getConditionFromStatus = status => {
  if (status === 'quarantine') return 'quarantine';
  if (status === 'write-off') return 'write-off';
  return 'ok';
};

/**
 * Rollback function
 */
const rollbackEquipmentReasons = async () => {
  try {
    console.log('Rolling back equipment reasons migration...');

    const result = await Equipment.updateMany(
      {
        $or: [{ reasonStatus: { $ne: '' } }, { reason: { $ne: null } }],
      },
      {
        $set: {
          reasonStatus: '',
          reason: null,
          updatedAt: new Date(),
        },
      }
    );

    console.log(`Rollback completed: ${result.modifiedCount} equipment records updated`);
  } catch (error) {
    console.error('Error during rollback:', error);
    throw error;
  }
};

module.exports = {
  up: migrateEquipmentReasonsImproved,
  down: rollbackEquipmentReasons,
};

// Run directly if called
if (require.main === module) {
  migrateEquipmentReasonsImproved()
    .then(() => {
      console.log('Migration completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
