const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const PmOrderManageEquipment = require('../models/pm-order-manage-equipment.model');

/**
 * Seeder to migrate existing reason data from equipment-order-history to equipment model
 * This ensures existing mobile data is preserved and accessible to web portal
 */
const migrateEquipmentReasons = async () => {
  try {
    console.log('Starting equipment reasons migration...');

    // Find all equipment order histories with reason data
    const equipmentHistories = await EquipmentOrderHistory.find({
      $or: [
        { wmDispatchReasonStatus: { $ne: '' } },
        { wmDispatchReason: { $ne: null } },
        { checkinReasonStatus: { $ne: '' } },
        { checkinReason: { $ne: null } },
      ],
      deletedAt: null,
    }).populate('equipment');

    console.log(`Found ${equipmentHistories.length} equipment histories with reason data`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const history of equipmentHistories) {
      if (!history.equipment) {
        console.log(`Skipping history ${history._id} - equipment not found`);
        skippedCount++;
        continue;
      }

      // Determine the most recent reason data to use
      let reasonStatus = '';
      let reason = null;
      let condition = history.equipment.condition || 'ok';

      // Priority: wmDispatch reasons (from mobile linking) over checkin reasons
      if (history.wmDispatchReasonStatus && history.wmDispatchReasonStatus !== '') {
        reasonStatus = history.wmDispatchReasonStatus;
        reason = history.wmDispatchReason;

        // Set condition based on subStatus if available
        if (history.subStatus === 'quarantine') {
          condition = 'quarantine';
        } else if (history.subStatus === 'write-off') {
          condition = 'write-off';
        }
      } else if (history.checkinReasonStatus && history.checkinReasonStatus !== '') {
        reasonStatus = history.checkinReasonStatus;
        reason = history.checkinReason;

        // Set condition based on subStatus if available
        if (history.subStatus === 'quarantine') {
          condition = 'quarantine';
        } else if (history.subStatus === 'write-off') {
          condition = 'write-off';
        }
      }

      // Update equipment with reason data
      try {
        await Equipment.findByIdAndUpdate(history.equipment._id, {
          $set: {
            reasonStatus: reasonStatus,
            reason: reason,
            condition: condition,
            updatedAt: new Date(),
          },
        });

        console.log(
          `Updated equipment ${history.equipment._id} with reason: ${reasonStatus} - ${reason}`
        );
        updatedCount++;
      } catch (error) {
        console.error(`Error updating equipment ${history.equipment._id}:`, error.message);
        skippedCount++;
      }
    }

    console.log('Equipment Order History Migration completed:');
    console.log(`- Updated: ${updatedCount} equipment records`);
    console.log(`- Skipped: ${skippedCount} records`);

    // Also migrate PM Order Manage Equipment data
    await migratePMOrderReasons();
  } catch (error) {
    console.error('Error during equipment reasons migration:', error);
    throw error;
  }
};

/**
 * Migrate PM Order Manage Equipment reason data to equipment model
 */
const migratePMOrderReasons = async () => {
  try {
    console.log('\nStarting PM Order Manage Equipment reasons migration...');

    // Find all PM order manage equipment with reason data
    const pmOrderManageEquipments = await PmOrderManageEquipment.find({
      $or: [
        { checkinReasonStatus: { $ne: '' } },
        { checkinReason: { $ne: null } },
        { checkoutReasonStatus: { $ne: '' } },
        { checkoutReason: { $ne: null } },
      ],
      deletedAt: null,
    }).populate('equipment');

    console.log(
      `Found ${pmOrderManageEquipments.length} PM order manage equipments with reason data`
    );

    let updatedCount = 0;
    let skippedCount = 0;

    for (const pmOrderManage of pmOrderManageEquipments) {
      if (!pmOrderManage.equipment || pmOrderManage.equipment.length === 0) {
        console.log(`Skipping PM order manage ${pmOrderManage._id} - no equipment found`);
        skippedCount++;
        continue;
      }

      // Determine the most recent reason data to use
      let reasonStatus = '';
      let reason = null;
      let condition = 'ok';

      // Priority: checkout reasons over checkin reasons (more recent)
      if (pmOrderManage.checkoutReasonStatus && pmOrderManage.checkoutReasonStatus !== '') {
        reasonStatus = pmOrderManage.checkoutReasonStatus;
        reason = pmOrderManage.checkoutReason;

        // Set condition based on checkoutStatus
        if (pmOrderManage.checkoutStatus === 'quarantine') {
          condition = 'quarantine';
        } else if (pmOrderManage.checkoutStatus === 'write-off') {
          condition = 'write-off';
        }
      } else if (pmOrderManage.checkinReasonStatus && pmOrderManage.checkinReasonStatus !== '') {
        reasonStatus = pmOrderManage.checkinReasonStatus;
        reason = pmOrderManage.checkinReason;

        // Set condition based on receivedStatus
        if (pmOrderManage.receivedStatus === 'quarantine') {
          condition = 'quarantine';
        } else if (pmOrderManage.receivedStatus === 'write-off') {
          condition = 'write-off';
        }
      }

      // Update all equipment in this PM order manage
      for (const equipmentId of pmOrderManage.equipment) {
        try {
          await Equipment.findByIdAndUpdate(equipmentId, {
            $set: {
              reasonStatus: reasonStatus,
              reason: reason,
              condition: condition,
              updatedAt: new Date(),
            },
          });

          console.log(
            `Updated equipment ${equipmentId} from PM order with reason: ${reasonStatus} - ${reason}`
          );
          updatedCount++;
        } catch (error) {
          console.error(`Error updating equipment ${equipmentId}:`, error.message);
          skippedCount++;
        }
      }
    }

    console.log('PM Order Migration completed:');
    console.log(`- Updated: ${updatedCount} equipment records`);
    console.log(`- Skipped: ${skippedCount} records`);
  } catch (error) {
    console.error('Error during PM order reasons migration:', error);
    throw error;
  }
};

/**
 * Rollback function to remove reason data from equipment model
 */
const rollbackEquipmentReasons = async () => {
  try {
    console.log('Rolling back equipment reasons migration...');

    const result = await Equipment.updateMany(
      {
        $or: [{ reasonStatus: { $ne: '' } }, { reason: { $ne: null } }],
      },
      {
        $set: {
          reasonStatus: '',
          reason: null,
          updatedAt: new Date(),
        },
      }
    );

    console.log(`Rollback completed: ${result.modifiedCount} equipment records updated`);
  } catch (error) {
    console.error('Error during rollback:', error);
    throw error;
  }
};

module.exports = {
  up: migrateEquipmentReasons,
  down: rollbackEquipmentReasons,
};
