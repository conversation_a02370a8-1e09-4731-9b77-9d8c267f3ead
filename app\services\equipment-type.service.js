const EquipmentType = require('../models/equipment-type.model');

/**
 * Create EquipmentType
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentType = async requestData => {
  return await EquipmentType.create(requestData);
};

/**
 * Filter EquipmentCategories
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentType = async (filter, page, perPage, sort) => {
  return await EquipmentType.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'equipmentCategory',
        model: 'equipment-category',
        select: { name: 1, abbreviation: 1 },
      },
      {
        path: 'equipmentUnit',
        model: 'equipment-unit',
        select: { title: 1, abbreviation: 1 },
      },
      {
        path: 'currencyUnit',
        model: 'currency-unit',
        select: { name: 1, symbol: 1 },
      },
      {
        path: 'quantityType',
        model: 'equipment-quantity-type',
        select: { name: 1, priceType: 1, quantityType: 1, isActive: 1 },
      },
      {
        path: 'hsCode',
        model: 'hs-code',
        select: { name: 1, code: 1 },
      },
      {
        path: 'certificateTypes',
        model: 'equipment-certificate-type',
        select: { _id: 1, title: 1, isValidityDate: 1 },
      },
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'ceNorms',
        model: 'ce-norms',
        select: { _id: 1, name: 1, month: 1 },
      },
    ]);
};

/**
 * Get Equipment Types
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getEquipmentTypes = async (filter, page, perPage, sort, search) => {
  let pipeline = [
    {
      $match: filter,
    },
    {
      $sort: sort,
    },
    {
      $match: {
        type: {
          $regex: search,
          $options: 'i',
        },
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: '_id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $group: {
              _id: '$equipmentType',
              lastEquipmentImage: { $first: '$equipmentImage' },
            },
          },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$lastEquipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategory',
        pipeline: [
          {
            $project: {
              createdBy: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategory',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'quantityType',
        foreignField: '_id',
        as: 'quantityType',
        pipeline: [
          {
            $project: {
              _id: 0,
              createdBy: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: '$quantityType',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'currencyUnit',
        foreignField: '_id',
        as: 'currencyUnit',
        pipeline: [
          {
            $project: {
              _id: 0,
              createdBy: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnit',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnit',
        pipeline: [
          {
            $project: {
              _id: 0,
              createdBy: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnit',
    },
    {
      $project: {
        _id: 1,
        type: 1,
        price: 1,
        lastEquipmentImage: {
          $cond: {
            if: { $eq: ['$associatedEquipment.lastEquipmentImage', []] },
            then: {},
            else: { $arrayElemAt: ['$associatedEquipment.lastEquipmentImage', 0] },
          },
        },
        equipmentCategory: 1,
        quantityType: 1,
        currencyUnit: 1,
        equipmentUnit: 1,
        isTemporary: '$equipmentCategory.isTemporary',
      },
    },
  ];

  if (page == '' && perPage == '') {
    return await EquipmentType.aggregate(pipeline);
  }

  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );
  let coll = { collation: { locale: 'en' } };
  return await EquipmentType.aggregate(pipeline, coll);
};

/**
 * Get EquipmentType by Id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentTypeById = async id => {
  return await EquipmentType.findOne(
    { _id: id, deletedAt: null, isActive: true },
    {
      deletedAt: 0,
      deletedBy: 0,
      __v: 0,
    }
  ).populate([
    {
      path: 'equipmentCategory',
      model: 'equipment-category',
      select: { name: 1, abbreviation: 1 },
    },
    {
      path: 'equipmentUnit',
      model: 'equipment-unit',
      select: { title: 1, abbreviation: 1 },
    },
    {
      path: 'currencyUnit',
      model: 'currency-unit',
      select: { name: 1, symbol: 1, isDefault: 1 },
    },
    {
      path: 'quantityType',
      model: 'equipment-quantity-type',
      select: { name: 1, priceType: 1, quantityType: 1 },
    },
    {
      path: 'certificateTypes',
      model: 'equipment-certificate-type',
      select: { _id: 1, title: 1, isValidityDate: 1 },
    },
    {
      path: 'hsCode',
      model: 'hs-code',
      select: { name: 1, code: 1 },
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentType
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentType = async (id, requestData) => {
  return await EquipmentType.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentType
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentType = async (id, deletedAt) => {
  return await EquipmentType.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Fetch EquipmentType By Id
 *
 * @param {*} id
 * @returns
 */
exports.fetchEquipmentTypeByIdForValidation = async id => EquipmentType.findById(id);

/**
 * Get Equipment Type By Filter
 *
 * @param {*} filter
 * @returns
 */
exports.getEquipmentTypeByFilter = async filter => {
  return await EquipmentType.findOne(filter);
};

/**
 * Count EquipmentType
 *
 * @param {*} filter
 * @returns
 */
exports.countEquipmentType = async filter => {
  return await EquipmentType.countDocuments(filter);
};
