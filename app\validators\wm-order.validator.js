const { body, param, constantUtils, commonUtils } = require('../validators/parent.validator');

exports.approveRejectPMOrderValidationRule = () => {
  return [
    body('pmOrderManage').isArray().notEmpty().withMessage(constantUtils.PM_ORDER_DATA_REQUIRED),
  ];
};

exports.linkEquipmentWithOrderValidationRule = () => {
  return [
    body('equipmentId').notEmpty().withMessage(constantUtils.QR_CODE_REQUIRED),
    body('quantity').notEmpty().withMessage(constantUtils.QUANTITY_REQUIRED),
    // Validation for quarantine/write-off reason requirements
    // body('subStatus')
    //   .optional()
    //   .custom((value, { req }) => {
    //     if ((value === 'quarantine' || value === 'write-off') && !req.body.wmDispatchReasonStatus) {
    //       throw new Error('Reason status is required when subStatus is quarantine or write-off');
    //     }
    //     return true;
    //   }),
    // body('wmDispatchReasonStatus')
    //   .optional()
    //   .custom((value, { req }) => {
    //     if (
    //       value === 'other' &&
    //       (!req.body.wmDispatchReason || req.body.wmDispatchReason.trim() === '')
    //     ) {
    //       throw new Error('Custom reason is required when reason status is "other"');
    //     }
    //     return true;
    //   }),
  ];
};

exports.updateOrderStatusValidationRule = () => {
  return [body('status').notEmpty().withMessage(constantUtils.STATUS_REQUIRED)];
};

exports.validateParamIds = () => {
  return [
    param('orderId').custom(value => {
      if (value && !commonUtils.isValidId(value)) {
        throw new Error(constantUtils.INVALID_ORDER_ID);
      }
      return true;
    }),
  ];
};
