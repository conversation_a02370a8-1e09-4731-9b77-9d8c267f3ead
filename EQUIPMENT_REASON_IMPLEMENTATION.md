# Equipment Quarantine & Write-Off Reason Implementation

## Overview

This implementation adds quarantine and write-off reason functionality to the web portal, matching the existing mobile app behavior. The solution adds reason fields to the equipment model to serve as a single source of truth for both mobile and web platforms.

The implementation handles both **WM (Warehouse Manager)** and **PM (Project Manager)** flows:

### WM Flow (Warehouse Inventory)
- Updates `equipment-order-history` with `wmDispatchReasonStatus` and `wmDispatchReason`
- API: `/api/wm-order/link-equipment/:pmOrderManageId`

### PM Flow (Project Inventory) 
- Updates `pm-order-manage-equipment` with `checkinReasonStatus`/`checkinReason` and `checkoutReasonStatus`/`checkoutReason`
- APIs: PM inventory management and checkout APIs

## Changes Made

### 1. Equipment Model Updates (`app/models/equipment.model.js`)

Added two new fields to the equipment schema:

```javascript
reasonStatus: {
  type: String,
  enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
  default: '',
},
reason: {
  type: String,
  default: null,
}
```

### 2. Data Migration (`app/seeders/migrate-equipment-reasons.seeder.js`)

Created a comprehensive seeder to migrate existing reason data from both:
- `equipment-order-history` (WM flow): `wmDispatchReasonStatus`, `wmDispatchReason`, `checkinReasonStatus`, `checkinReason`
- `pm-order-manage-equipment` (PM flow): `checkinReasonStatus`, `checkinReason`, `checkoutReasonStatus`, `checkoutReason`

### 3. Mobile API Synchronization

#### WM Flow - Link Equipment API (`app/controllers/wm-order.controller.js`)
- `linkEquipmentWithOrder`: Syncs `wmDispatchReasonStatus` → `equipment.reasonStatus`
- `warehouseCheckIn`: Syncs `checkinReasonStatus` → `equipment.reasonStatus`

#### PM Flow - APIs (`app/controllers/pm-order.controller.js` & `wm-order.controller.js`)
- `manageInventory`: Syncs PM `checkinReasonStatus` → `equipment.reasonStatus`
- `checkAndChangeOrderStatus`: Syncs PM `checkoutReasonStatus` → `equipment.reasonStatus`

## Data Flow Examples

### WM Flow Example
```
Mobile WM: Set status=write-off, reason=other, reason="hello its other reason"
    ↓
equipment-order-history: 
  - subStatus: "write-off"
  - wmDispatchReasonStatus: "other"  
  - wmDispatchReason: "hello its other reason"
    ↓
equipment:
  - condition: "write-off"
  - reasonStatus: "other"
  - reason: "hello its other reason"
    ↓
Web Portal: Displays write-off status with reason
```

### PM Flow Example
```
Mobile PM: Set status=write-off, reason=other, reason="its a op product you know"
    ↓
pm-order-manage-equipment:
  - checkinReasonStatus: "other"
  - checkinReason: "It's op product you know"
    ↓
equipment:
  - condition: "write-off"
  - reasonStatus: "other"
  - reason: "It's op product you know"
    ↓
Web Portal: Displays write-off status with reason
```

## API Usage

### Web Portal Equipment APIs

#### GET `/api/equipments/:id`
```json
{
  "_id": "67b31977db6006fa57610cb4",
  "name": "Equipment Name",
  "condition": "quarantine",
  "reasonStatus": "repair-required",
  "reason": "Custom reason text"
}
```

#### PATCH `/api/equipments/:id`
```json
{
  "condition": "quarantine",
  "reasonStatus": "repair-required",
  "reason": "Equipment needs maintenance"
}
```

### Mobile APIs (Enhanced)

#### WM Flow - PATCH `/api/wm-order/link-equipment/:pmOrderManageId`
```json
{
  "equipmentId": "67b31977db6006fa57610cb4",
  "quantity": 1,
  "subStatus": "quarantine",
  "wmDispatchReasonStatus": "repair-required",
  "wmDispatchReason": "Custom reason"
}
```

#### PM Flow - Inventory Management
```json
{
  "status": "check-in",
  "subStatus": "write-off",
  "checkinReasonStatus": "other",
  "checkinReason": "It's op product you know",
  "orderId": "order-id",
  "equipment": ["equipment-id-1"],
  "equipmentType": "equipment-type-id"
}
```

## Reason Status Options

### For Quarantine:
- `repair-required` - Repair required
- `certification-required` - Certification required
- `other` - Other (requires custom reason text)

### For Write-Off:
- `damaged` - Damaged
- `missing` - Missing
- `other` - Other (requires custom reason text)

## Implementation Benefits

1. **Complete Flow Coverage**: Handles both WM and PM flows
2. **Single Source of Truth**: Equipment reason stored directly on equipment model
3. **Bi-directional Sync**: Mobile and web changes synchronized automatically
4. **Backward Compatibility**: Existing mobile functionality continues unchanged
5. **Data Integrity**: Validation ensures reason provided when required
6. **Performance**: No need to query order history for current equipment status

## Migration Steps

1. **Deploy Model Changes**: Update equipment model with new fields
2. **Run Data Migration**: Execute seeder to migrate existing WM and PM flow data
3. **Deploy API Changes**: Update controllers and validators
4. **Test Integration**: Verify mobile-web synchronization for both flows
5. **Update Frontend**: Modify web portal UI to display and edit reason fields

## Files Modified/Created

- `app/models/equipment.model.js` - Added reason fields
- `app/validators/equipment.validator.js` - Added validation rules
- `app/validators/wm-order.validator.js` - Added mobile API validation
- `app/controllers/wm-order.controller.js` - Added WM flow reason synchronization
- `app/controllers/pm-order.controller.js` - Added PM flow reason synchronization
- `app/seeders/migrate-equipment-reasons.seeder.js` - Data migration for both flows
- `EQUIPMENT_REASON_IMPLEMENTATION.md` - Complete documentation

This implementation now fully supports both WM and PM flows, ensuring that quarantine and write-off reasons set from mobile are automatically synchronized to the equipment model and available to the web portal.
