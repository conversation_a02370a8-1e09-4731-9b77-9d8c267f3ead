const { body, constantUtils, validateParamIds } = require('../validators/parent.validator');
// Service
const equipmentService = require('../services/equipment.service');
const equipmentTypeService = require('../services/equipment-type.service');

const createEquipmentValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED),
    body('serialNumber').custom(async value => {
      if (value !== null) {
        const existingEquipment = await equipmentService.getSingleEquipmentByFilter({
          serialNumber: value,
        });
        if (existingEquipment) {
          throw new Error(constantUtils.DUPLICATE_SERIAL_NUMBER);
        }
      }
      return true;
    }),
    body('weight')
      .notEmpty()
      .withMessage(constantUtils.WEIGHT_REQUIRED)
      .custom(value => {
        if (value && value < 0) {
          throw new Error(constantUtils.INVALID_WEIGHT);
        }
        return true;
      }),
    body('equipmentType').isString().notEmpty().withMessage(constantUtils.EQUIPMENT_TYPE_REQUIRED),
    body('qrCode')
      .optional({ checkFalsy: false })
      .custom(async value => {
        if (value.length == 0) return true;
        let [qrCode] = value;
        let filter = { 'qrCode.code': qrCode.code };
        const exist = await equipmentService.checkUniqueQrCode(filter);
        if (exist) {
          throw new Error(constantUtils.DUPLICATE_QR_CODE);
        }
        return true;
      }),
    body('equipmentImage').custom(value => {
      if (
        typeof value !== 'undefined' &&
        value !== null &&
        value.length > global.constant.UPLOAD_FILE_LIMIT
      ) {
        throw new Error(constantUtils.INVALID_FILE_LIMIT);
      }
      return true;
    }),
  ];
};

const updateEquipmentValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .optional({ checkFalsy: false })
      .withMessage('Name is required'),
    body('serialNumber')
      .optional({ checkFalsy: false })
      .custom(async (value, { req }) => {
        if (value !== null) {
          const existingEquipment = await equipmentService.getSingleEquipmentByFilter({
            _id: { $ne: req.params.id },
            serialNumber: value,
          });
          if (existingEquipment) {
            throw new Error(constantUtils.DUPLICATE_SERIAL_NUMBER);
          }
        }
        return true;
      }),
    body('weight')
      .isNumeric()
      .notEmpty()
      .withMessage(constantUtils.WEIGHT_REQUIRED)
      .optional({ checkFalsy: false })
      .custom(value => {
        if (value && value < 0) {
          throw new Error(constantUtils.INVALID_WEIGHT);
        }
        return true;
      }),
    body('equipmentType')
      .isString()
      .notEmpty()
      .optional({ checkFalsy: false })
      .withMessage(constantUtils.EQUIPMENT_TYPE_REQUIRED)
      .custom(async value => {
        // Check if equipment type deleted
        if (value) {
          const exist = await equipmentTypeService.fetchEquipmentTypeByIdForValidation(value);
          if (exist?.deletedAt != null) {
            throw new Error(
              `Selected equipment type '${exist.type}' has been deleted. Please select new one.`
            );
          }
        }
        return true;
      }),
    body('equipmentImage')
      .optional({ checkFalsy: false })
      .custom(value => {
        if (
          typeof value !== 'undefined' &&
          value !== null &&
          value.length > global.constant.UPLOAD_FILE_LIMIT
        ) {
          throw new Error(constantUtils.INVALID_FILE_LIMIT);
        }
        return true;
      }),
    // body('condition')
    //   .optional({ checkFalsy: false })
    //   .isIn(['ok', 'quarantine', 'maintenance', 'write-off'])
    //   .withMessage('Invalid condition value'),
    // body('reasonStatus')
    //   .optional({ checkFalsy: false })
    //   .isIn(['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'])
    //   .withMessage('Invalid reason status value'),
    // body('reason')
    //   .optional({ checkFalsy: false })
    //   .isString()
    //   .withMessage('Reason must be a string'),
    // Custom validation to ensure reason is provided when condition is quarantine or write-off
    // body('condition')
    //   .optional({ checkFalsy: false })
    //   .custom((value, { req }) => {
    //     if ((value === 'quarantine' || value === 'write-off') && !req.body.reasonStatus) {
    //       throw new Error('Reason status is required when condition is quarantine or write-off');
    //     }
    //     return true;
    //   }),
    // body('reasonStatus')
    //   .optional({ checkFalsy: false })
    //   .custom((value, { req }) => {
    //     if (value === 'other' && (!req.body.reason || req.body.reason.trim() === '')) {
    //       throw new Error('Custom reason is required when reason status is "other"');
    //     }
    //     return true;
    //   }),
  ];
};

module.exports = {
  createEquipmentValidationRule,
  updateEquipmentValidationRule,
  validateParamIds,
};
